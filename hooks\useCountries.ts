import { useState, useEffect, useMemo } from 'react';
import { Country } from '@/types';

// Simuler un chargement lazy des pays
const loadCountries = async (): Promise<Country[]> => {
  // Simuler un délai de chargement
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Importer dynamiquement les pays
  const { COUNTRIES } = await import('@/constants/countries');
  return COUNTRIES;
};

export const useCountries = (searchTerm: string = '') => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await loadCountries();
        setCountries(data);
      } catch (err) {
        setError('Erreur lors du chargement des pays');
        console.error('Error loading countries:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrer et trier les pays en fonction du terme de recherche
  const filteredCountries = useMemo(() => {
    if (!countries.length) return [];

    // Toujours trier d'abord par nom commun
    const sortedCountries = [...countries].sort((a, b) =>
      a.name.common.localeCompare(b.name.common),
    );

    if (!searchTerm.trim()) {
      return sortedCountries;
    }

    const searchLower = searchTerm.toLowerCase().trim();

    // Recherche plus robuste avec plusieurs critères
    const filtered = sortedCountries.filter((country) => {
      const commonName = country.name.common.toLowerCase();
      const officialName = country.name.official.toLowerCase();

      // Recherche dans le nom commun et officiel
      return (
        commonName.includes(searchLower) ||
        officialName.includes(searchLower) ||
        commonName.startsWith(searchLower)
      );
    });

    // Trier les résultats de recherche par pertinence
    return filtered.sort((a, b) => {
      const aCommon = a.name.common.toLowerCase();
      const bCommon = b.name.common.toLowerCase();

      // Priorité aux correspondances exactes au début
      const aStartsWith = aCommon.startsWith(searchLower);
      const bStartsWith = bCommon.startsWith(searchLower);

      if (aStartsWith && !bStartsWith) return -1;
      if (!aStartsWith && bStartsWith) return 1;

      // Sinon, tri alphabétique
      return aCommon.localeCompare(bCommon);
    });
  }, [countries, searchTerm]);

  return {
    countries: filteredCountries,
    isLoading,
    error,
    totalCount: countries.length,
  };
};
